import React, { useEffect, useRef, useState } from 'react';
import { cn } from '../../utils/utils';
import { getTokens } from '../../utils/localStorage';
import dayjs from 'dayjs';
import { RiCopperCoinLine, RiSendPlane2Line } from '@remixicon/react';
import ViewChat from './ViewChat';
import { Button } from '../ui/button';
import { Box } from '../Box';
import { useAppDispatch, useAppSelector } from '../../store';
import { setMessageAI } from '../../store/redux/AIAgent/slice';
import SelectOptions from '../SelectOptions';

const VERSION_AI_AGENT = [{
  label: 'v1',
  value: 'v1'
}, {
  label: 'v2',
  value: 'v2'
}, {
  label: 'v3',
  value: 'v3'
}, {
  label: 'v4',
  value: 'v4'
}, {
  label: 'v5',
  value: 'v5'
}];

const SUGGESTION_MESSAGE = [
  'How to Find Audience?',
  'How to Add Segment to Contact List?',
  'How to Processing Data?'
];

interface ILimitToken {
  current: number,
  limit: number,
  over_usage: boolean
}

interface IChatBoxContainerProps {
  isPopup: boolean;
}

const DEFAULT_MESSAGE = 'Hello and welcome! Ready to explore potential leads together?';

const ChatBoxContainer: React.FC<IChatBoxContainerProps> = ({ isPopup }: IChatBoxContainerProps) => {
  const [messages, setMessages] = useState<{role: string; content: string}[]>([]);
  const [input, setInput] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [selectVersion, setSelectVersion] = useState<string>(VERSION_AI_AGENT[0].value);
  const { messageAiAgent } = useAppSelector((state) => state.aiAgent);
  const dispatch = useAppDispatch();

  const [limitToken, setLimitToken] = useState<ILimitToken>({
    current: 0,
    limit: 0,
    over_usage: false
  });
  const { accessToken } = getTokens();

  useEffect(() => {
    handleCheckToken().then((res) => {
      setLimitToken(res);
    });
  }, []);

  useEffect(() => {
    if (!!messageAiAgent.length) {
      setMessages(messageAiAgent);
    }
  }, [messageAiAgent]);

  const handleCheckToken = async () => {
    const response = await fetch(import.meta.env.REACT_APP_API_URL + `/ai-agent/credit-stats`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json', 'authorization': `Bearer ${accessToken}` }
    });
    const jsonRes: ILimitToken = await response.json();
    return jsonRes;
  };

  const sendMessage = async (sampleInput?: string) => {
    if (!input.trim() && !sampleInput?.trim()) {
      return;
    }

    const newMessages = [...messages, { role: 'user', content: sampleInput?.trim() || input.trim() }];
    setMessages(newMessages);
    setInput('');
    setLoading(true);

    try {
      const conversation_id = crypto.randomUUID();
      const response = await fetch(import.meta.env.REACT_APP_API_URL + `/ai-agent/query/${selectVersion}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'authorization': `Bearer ${accessToken}` },
        body: JSON.stringify({ messages: newMessages, stream: true, conversation_id })
      });

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let responseText = '';

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            handleCheckToken().then((res) => {
              setLimitToken(res);
            });
            break;
          }
          const chunk = decoder.decode(value);
          responseText += chunk;
          const newMess = [...newMessages, { role: 'assistant', content: responseText }];
          dispatch(setMessageAI(newMess));
          setMessages(newMess);
        }
      }
    } catch (err) {
      setMessages(prev => [...prev, { role: 'assistant', content: 'Big360 Assistant gặp sự cố khi xử lý yêu cầu. Vui lòng thử lại sau.' }]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [messages]);

  return (
    <div className="h-full flex-1">
      <div
        ref={chatContainerRef}
        className={cn('chat-container overflow-y-auto rounded-lg p-2 mb-2 bg-white', isPopup ?
          'h-[408px]' :
          'h-full max-h-[calc(100%-300px)]')}
      >
        {<div className="text-tertiary text-sm text-center py-2 mb-3">
          {dayjs().format('h:mm A DD/MM/YYYY')}
        </div>}
        <div
          className={cn('break-words text-left text-sm px-2 py-1 rounded-sm w-full mr-auto', isPopup ?
            'w-[302px]' :
            '')}
        >
          <ViewChat content={DEFAULT_MESSAGE} />
          {messages.length === 0 &&
            <Box variant={isPopup ? 'col-start' : 'row-start'} className="gap-3 mt-3">
              {SUGGESTION_MESSAGE.map((item, index) => {
                return <Button
                  className={cn('font-semibold text-sm text-secondary bg-[#F0F0F0] hover:bg-brand-default hover:text-white', isPopup ?
                    'w-[302px]' :
                    'w-fit')}
                  key={index}
                  onClick={() => sendMessage(item)}
                >
                  {item}
                </Button>;
              })}
            </Box>}
        </div>
        {messages.map((msg, idx) => (
          <div
            key={idx}
            className={cn('flex items-start p-[3px] gap-2 mb-4',
              msg.role === 'user' ? 'flex-row-reverse ml-auto' : 'flex-row mr-auto', isPopup ? 'w-fit' : 'w-full')}
          >
            <div className={cn(!isPopup && 'w-full')}>
              <div
                className={cn('break-words text-left text-sm px-2 py-1 rounded-sm', msg.role === 'user' ?
                  'text-secondary bg-custom-secondary ml-auto' :
                  ' mr-auto', isPopup ?
                  'w-[302px]' :
                  msg.role === 'user' ? 'w-fit' : 'w-full')}
              >
                <ViewChat content={msg.content} />
              </div>
            </div>

          </div>
        ))}

        {loading && (
          <div className="flex items-center justify-center text-center py-2 text-[#6e6e80] animate-pulse gap-1">
            <div className="balls">
              <div />
              <div />
              <div />
            </div>
          </div>
        )}
      </div>
      <div className={cn(isPopup ? '' : 'sticky bottom-0 bg-white')}>
        <div className="flex items-center justify-between mb-2">
          <SelectOptions
            className="border border-[#E1E2E3]"
            options={VERSION_AI_AGENT}
            selected={selectVersion}
            disabled={loading || limitToken.over_usage}
            onChange={(value) => setSelectVersion(value)}
            placeholder={'version'}
          />
          <div className="text-primary text-sm flex items-center justify-end  mb-1">
            <RiCopperCoinLine size={16} color="#5A18BF" />
            <span className="font-medium text-brand-default">{limitToken.current}</span>/
            <span className="font-medium text-brand-default">{limitToken.limit}</span>
          </div>
        </div>

        {limitToken.over_usage ? <></> :
          <div className="relative flex mb-2 gap-1">
            <textarea
              className={cn('w-full flex-grow rounded-lg px-3 py-2 pr-12 text-base resize-none focus:outline-none focus:border-brand-default focus:ring-2 focus:ring-brand-light', !isPopup && 'border border-[#E1E2E3]')}
              placeholder="Type a message..."
              value={input || ''}
              rows={isPopup ? 2 : 5}
              disabled={loading || limitToken.over_usage}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  sendMessage();
                }
              }}
            />
            <button
              className="absolute right-3 bottom-3 text-tertiary disabled:text-gray-300"
              onClick={() => sendMessage()}
              disabled={!input.trim() || loading || limitToken.over_usage}
            >
              <RiSendPlane2Line size={20} color={'#515667'} />
            </button>
          </div>
        }
      </div>
    </div>
  );
};

export default ChatBoxContainer;
