import { Popover, PopoverContent, PopoverTrigger } from '../../../components/ui/popover';
import { RiExternalLinkLine } from '@remixicon/react';
import { Button } from '../../../components/ui/button';
import { useState } from 'react';
import { Box } from '../../../components/Box';
import { FolderCloseLine } from '../../../assets/icons/FolderCloseLineIcon';
import { Link } from 'react-router-dom';
import { LoadingButtonIcon } from '../../../assets/icons/LoadingButtonIcon';
import { cn, formatDate } from '../../../utils/utils';
import { useAppDispatch, useAppSelector } from '../../../store';
import { handleGetSegmentLog } from '../../../store/redux/SegmentLog/slice';
import { Badge } from '../../../components/ui/badge';
import { ShareFolderIcon } from '../../../assets/icons/ShareFolderIcon';

export const SegmentLog = () => {
  const { logSegment, loading } = useAppSelector((state) => state.segmentLog);
  const dispatch = useAppDispatch();
  const [isOpen, setIsOpen] = useState(false);

  const handleConvertStatus = (status: string | number) => {
    switch (status) {
      case 0:
        return <Badge variant={'error'}>Failed</Badge>;
      case 1:
        return <Badge variant={'success'}>Success</Badge>;
      case 2:
        return <Badge variant={'process'}>In progress</Badge>;
    }
  };

  const handlePopoverOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (open) {
      // Fetch segment log when popover opens
      dispatch(handleGetSegmentLog()).unwrap();
    }
  };

  const handleConvertType = (type: string) => {
    switch (type) {
      case 'social':
        return 'Social';
      case 'persona':
        return 'Work Persona';
      default:
        return '';
    }
  };

  const handleConvertAdd = (status: string | number) => {
    switch (status) {
      case 1:
        return 'Added';
      case 0:
      case 2:
        return 'Adding';
    }
  };
  console.log({logSegment});
  const isActive = logSegment?.items?.some((item) => {item.status === 2});
  console.log({isActive});
  return (
    <Popover open={isOpen} onOpenChange={handlePopoverOpenChange}>
      <PopoverTrigger asChild>
        <Button variant={'secondary'} className="bg-[#F0F0F0] flex gap-2 border-none">
          <ShareFolderIcon isActive={true}/>
          Segment Log
        </Button>
      </PopoverTrigger>
      <PopoverContent
        align="end"
        className="w-full z-[9999] bg-[#F0F0F0] rounded-2xl border-none max-w-[calc(100%-16px)] md:min-w-[403px] p-3 shadow-[0_0_32px_0_rgba(9,10,13,0.02),_0_4px_20px_-8px_rgba(9,10,13,0.10)]"
      >
        <Box className="w-full">
          <p className="text-primary text-sm font-semibold py-[10px]">Recent segment added</p>
        </Box>
        {loading ? <LoadingButtonIcon /> : <>
          <div className={cn('rounded-lg overflow-auto h-full max-h-[320px] ', !!logSegment?.items.length && 'mt-[12px]')}>
            {logSegment?.items?.map((item) => (
              <div className="flex p-2 bg-white w-full max-w-md border border-[#E1E2E3]" key={item.segment_id}>
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-[#E1E2E3] flex items-center justify-center rounded-full">
                    <FolderCloseLine />
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm text-[#515667]">
                    {handleConvertAdd(item.status)} contacts from <span className="font-semibold text-primary">{handleConvertType(item.type)}{' '}{item.segment_name}</span> to Contact List
                  </p>
                  <div className="flex gap-1 items-center my-1">
                    {handleConvertStatus(item.status)}
                    <p className="text-xs text-[#515667]">
                      Contact import at: {formatDate(item.date_import)}
                    </p>
                  </div>
                  {item.status === 1 &&
                    <p
                      className="text-sm text-[#515667] underline"
                    >
                      <Link
                        to={`/crm360/contact`}
                        className="flex gap-1"
                      >
                        Go to contact list
                        <RiExternalLinkLine
                          size={16}
                          color={'#515667'}
                        />
                      </Link>
                    </p>}

                </div>
              </div>
            ))}
          </div>
        </>}
      </PopoverContent>
    </Popover>
  );
};
